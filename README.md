# AiLex Legal Assistant

A modern legal assistant application built with React, TypeScript, and Express.

## Features

- **Smart Contact Forms**: Enhanced UX with real-time email validation and consistent firstName/lastName fields
- **Responsive Design**: Optimized for all devices with modern UI components
- **API Integration**: Serverless functions for contact submissions and newsletter signups

## Development Setup

### Environment Variables

Before running the application, you need to set up environment variables. Copy `.env.example` to `.env.local` and fill in your values:

```bash
cp .env.example .env.local
```

Required environment variables:
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key (for server-side operations)
- `RESEND_API_KEY`: Your Resend API key for sending emails (optional)

### Vercel Deployment

For Vercel deployment, add these environment variables in your Vercel dashboard:
1. Go to your project settings
2. Navigate to Environment Variables
3. Add the required variables for Production, Preview, and Development environments

### Code Quality

This project includes automated code quality gates:
- **Pre-commit hooks**: Prettier formatting and TypeScript checking
- **GitHub Actions CI**: Lint, typecheck, build verification, and security audits

## Available Scripts

- `pnpm run dev` - Start development server
- `pnpm run build` - Build for production
- `pnpm run typecheck` - Run TypeScript type checking
- `pnpm run lint` - Run ESLint
- `pnpm run format` - Format code with Prettier
- `pnpm run format:check` - Check code formatting

## CI/CD Pipeline

The project uses GitHub Actions for continuous integration:
- **Automated testing** on every PR to develop
- **Bundle size monitoring** (400KB limit)
- **Security vulnerability scanning**
- **Code quality gates** with ESLint and TypeScript
