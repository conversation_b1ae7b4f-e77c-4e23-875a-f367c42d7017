import { VercelRequest, VercelResponse } from "@vercel/node";
import { z } from "zod";

// Validation schema for prospect signup
const prospectSignupSchema = z.object({
  email: z.string().email("Invalid email address"),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  phone: z.string().optional(),
  signupSource: z.enum(['website', 'landing_page', 'referral', 'social_media', 'advertisement', 'other']).default('website'),
  signupPage: z.string().optional(),
  utmSource: z.string().optional(),
  utmMedium: z.string().optional(),
  utmCampaign: z.string().optional(),
  utmContent: z.string().optional(),
  utmTerm: z.string().optional(),
  practiceAreaInterest: z.array(z.enum(['personal_injury', 'criminal_defense', 'family_law'])).optional(),
  caseUrgency: z.enum(['immediate', 'within_month', 'within_quarter', 'planning_ahead']).optional(),
  estimatedCaseValue: z.enum(['under_10k', '10k_50k', '50k_100k', 'over_100k', 'unknown']).optional(),
  newsletterSubscribed: z.boolean().default(true),
  marketingConsent: z.boolean().default(false),
  communicationPreferences: z.object({
    email: z.boolean().default(true),
    sms: z.boolean().default(false),
    phone: z.boolean().default(false),
  }).optional(),
  gdprConsent: z.boolean().refine(val => val === true, {
    message: "GDPR consent is required"
  }),
  turnstileToken: z.string().optional(),
});

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Enable CORS
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Access-Control-Allow-Methods", "POST, OPTIONS");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type");

  if (req.method === "OPTIONS") {
    return res.status(200).end();
  }

  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method Not Allowed" });
  }

  // Check environment variables
  const piLawyerApiUrl = process.env.NEXT_PUBLIC_PI_LAWYER_API_URL;
  
  if (!piLawyerApiUrl) {
    console.error("Missing PI Lawyer API URL configuration");
    return res.status(500).json({
      error: "Server configuration error",
      details: "Missing backend API configuration"
    });
  }

  try {
    // Validate request body
    const prospectData = prospectSignupSchema.parse(req.body);

    // Add client IP for GDPR compliance
    const clientIp = req.headers['x-forwarded-for'] || req.headers['x-real-ip'] || 'unknown';
    
    // Prepare data for PI Lawyer AI backend
    const backendPayload = {
      ...prospectData,
      gdprConsentDate: new Date().toISOString(),
      gdprConsentIp: Array.isArray(clientIp) ? clientIp[0] : clientIp,
    };

    // Call PI Lawyer AI backend
    const backendResponse = await fetch(`${piLawyerApiUrl}/api/prospects/signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'AiLex-Website/1.0',
      },
      body: JSON.stringify(backendPayload),
    });

    const backendData = await backendResponse.json();

    if (!backendResponse.ok) {
      console.error("Backend API error:", {
        status: backendResponse.status,
        data: backendData
      });
      
      return res.status(backendResponse.status).json({
        error: backendData.error || "Failed to process signup",
        message: backendData.message || "Please try again later"
      });
    }

    // Return success response
    res.status(200).json({
      success: true,
      message: backendData.message || "Successfully signed up for updates",
      prospectId: backendData.prospectId,
      emailVerified: backendData.emailVerified || false,
    });

  } catch (error) {
    console.error("Prospect signup API error:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: "Invalid form data",
        details: error.format(),
      });
    }

    if (error instanceof Error && error.message.includes('fetch')) {
      return res.status(503).json({
        error: "Service temporarily unavailable",
        message: "Unable to connect to backend service. Please try again later."
      });
    }

    res.status(500).json({ 
      error: "Internal server error",
      message: "An unexpected error occurred. Please try again."
    });
  }
}
