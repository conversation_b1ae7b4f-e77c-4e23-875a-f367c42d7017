import { VercelRequest, VercelResponse } from "@vercel/node";
import { z } from "zod";

// Validation schemas
const verifyEmailSchema = z.object({
  token: z.string().min(1, "Verification token is required"),
});

const checkEmailSchema = z.object({
  email: z.string().email("Invalid email address"),
});

export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Enable CORS
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Access-Control-Allow-Methods", "PUT, GET, OPTIONS");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type");

  if (req.method === "OPTIONS") {
    return res.status(200).end();
  }

  // Check environment variables
  const piLawyerApiUrl = process.env.NEXT_PUBLIC_PI_LAWYER_API_URL;
  
  if (!piLawyerApiUrl) {
    console.error("Missing PI Lawyer API URL configuration");
    return res.status(500).json({
      error: "Server configuration error",
      details: "Missing backend API configuration"
    });
  }

  try {
    if (req.method === "PUT") {
      // Verify email with token
      const { token } = verifyEmailSchema.parse(req.body);

      const backendResponse = await fetch(`${piLawyerApiUrl}/api/prospects/verify-email`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'AiLex-Website/1.0',
        },
        body: JSON.stringify({ token }),
      });

      const backendData = await backendResponse.json();

      if (!backendResponse.ok) {
        console.error("Backend verification error:", {
          status: backendResponse.status,
          data: backendData
        });
        
        return res.status(backendResponse.status).json({
          success: false,
          error: backendData.error || "Email verification failed",
          message: backendData.message || "Invalid or expired verification token"
        });
      }

      return res.status(200).json({
        success: true,
        message: backendData.message || "Email verified successfully",
      });

    } else if (req.method === "GET") {
      // Check email verification status
      const email = req.query.email as string;
      
      if (!email) {
        return res.status(400).json({
          error: "Email parameter is required"
        });
      }

      const { email: validatedEmail } = checkEmailSchema.parse({ email });

      const params = new URLSearchParams({ email: validatedEmail });
      const backendResponse = await fetch(`${piLawyerApiUrl}/api/prospects/verify-email?${params}`, {
        method: 'GET',
        headers: {
          'User-Agent': 'AiLex-Website/1.0',
        },
      });

      const backendData = await backendResponse.json();

      if (!backendResponse.ok) {
        console.error("Backend status check error:", {
          status: backendResponse.status,
          data: backendData
        });
        
        return res.status(backendResponse.status).json({
          error: backendData.error || "Failed to check verification status",
          message: backendData.message || "Please try again later"
        });
      }

      return res.status(200).json({
        emailVerified: backendData.emailVerified || false,
      });

    } else {
      return res.status(405).json({ error: "Method Not Allowed" });
    }

  } catch (error) {
    console.error("Email verification API error:", error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: "Invalid request data",
        details: error.format(),
      });
    }

    if (error instanceof Error && error.message.includes('fetch')) {
      return res.status(503).json({
        error: "Service temporarily unavailable",
        message: "Unable to connect to backend service. Please try again later."
      });
    }

    res.status(500).json({ 
      error: "Internal server error",
      message: "An unexpected error occurred. Please try again."
    });
  }
}
