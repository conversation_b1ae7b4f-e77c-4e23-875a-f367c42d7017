import { ProspectSignupRequest, ProspectSignupResponse, EmailVerificationRequest } from '@/types';
import { getStoredUTMParameters, getCurrentPageUrl } from './utils';

const PI_LAWYER_API_URL = import.meta.env.VITE_PI_LAWYER_API_URL || 'https://your-pi-lawyer-ai.vercel.app';

export class ProspectService {
  private static async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${PI_LAWYER_API_URL}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || data.message || 'Request failed');
    }

    return data;
  }

  static async signupProspect(
    prospectData: Omit<ProspectSignupRequest, 'signupSource' | 'signupPage' | 'utmSource' | 'utmMedium' | 'utmCampaign' | 'utmContent' | 'utmTerm'>
  ): Promise<ProspectSignupResponse> {
    // Automatically add UTM parameters and page tracking
    const utmParams = getStoredUTMParameters();
    const currentPage = getCurrentPageUrl();

    const fullProspectData: ProspectSignupRequest = {
      ...prospectData,
      signupSource: 'website',
      signupPage: currentPage,
      utmSource: utmParams.utm_source,
      utmMedium: utmParams.utm_medium,
      utmCampaign: utmParams.utm_campaign,
      utmContent: utmParams.utm_content,
      utmTerm: utmParams.utm_term,
    };

    return this.makeRequest<ProspectSignupResponse>('/api/prospects/signup', {
      method: 'POST',
      body: JSON.stringify(fullProspectData),
    });
  }

  static async verifyEmail(token: string): Promise<{ success: boolean; message: string }> {
    const requestData: EmailVerificationRequest = { token };

    return this.makeRequest('/api/prospects/verify-email', {
      method: 'PUT',
      body: JSON.stringify(requestData),
    });
  }

  static async checkEmailVerificationStatus(email: string): Promise<{ emailVerified: boolean }> {
    const params = new URLSearchParams({ email });
    return this.makeRequest(`/api/prospects/verify-email?${params}`);
  }
}

// Convenience functions for common use cases
export async function signupForNewsletter(
  email: string,
  options: {
    firstName?: string;
    lastName?: string;
    gdprConsent: boolean;
    marketingConsent?: boolean;
  }
): Promise<ProspectSignupResponse> {
  return ProspectService.signupProspect({
    email,
    firstName: options.firstName,
    lastName: options.lastName,
    newsletterSubscribed: true,
    marketingConsent: options.marketingConsent || false,
    gdprConsent: options.gdprConsent,
    communicationPreferences: {
      email: true,
      sms: false,
      phone: false,
    },
  });
}

export async function signupForContact(
  contactData: {
    email: string;
    firstName: string;
    lastName: string;
    phone?: string;
    message?: string;
    gdprConsent: boolean;
    practiceAreaInterest?: ('personal_injury' | 'criminal_defense' | 'family_law')[];
    caseUrgency?: 'immediate' | 'within_month' | 'within_quarter' | 'planning_ahead';
    estimatedCaseValue?: 'under_10k' | '10k_50k' | '50k_100k' | 'over_100k' | 'unknown';
  }
): Promise<ProspectSignupResponse> {
  return ProspectService.signupProspect({
    email: contactData.email,
    firstName: contactData.firstName,
    lastName: contactData.lastName,
    phone: contactData.phone,
    practiceAreaInterest: contactData.practiceAreaInterest,
    caseUrgency: contactData.caseUrgency,
    estimatedCaseValue: contactData.estimatedCaseValue,
    newsletterSubscribed: true,
    marketingConsent: true,
    gdprConsent: contactData.gdprConsent,
    communicationPreferences: {
      email: true,
      sms: !!contactData.phone,
      phone: !!contactData.phone,
    },
  });
}
