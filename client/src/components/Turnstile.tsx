import { useEffect, useRef, useState } from 'react';

interface TurnstileProps {
  onVerify: (token: string) => void;
  onError?: (error: string) => void;
  onExpire?: () => void;
  className?: string;
  theme?: 'light' | 'dark' | 'auto';
  size?: 'normal' | 'compact';
}

declare global {
  interface Window {
    turnstile: {
      render: (element: HTMLElement | string, options: any) => string;
      reset: (widgetId?: string) => void;
      remove: (widgetId?: string) => void;
      getResponse: (widgetId?: string) => string;
    };
    onloadTurnstileCallback: () => void;
  }
}

export default function Turnstile({
  onVerify,
  onError,
  onExpire,
  className = '',
  theme = 'light',
  size = 'normal',
}: TurnstileProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [widgetId, setWidgetId] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const siteKey = import.meta.env.VITE_TURNSTILE_SITE_KEY || '0x4AAAAAAA7TWjsvWoeed9DQ';

  useEffect(() => {
    if (!siteKey) {
      const errorMsg = 'Turnstile site key not configured';
      setError(errorMsg);
      onError?.(errorMsg);
      return;
    }

    // Check if Turnstile script is already loaded
    if (window.turnstile) {
      setIsLoaded(true);
      return;
    }

    // Load Turnstile script
    const script = document.createElement('script');
    script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
    script.async = true;
    script.defer = true;

    script.onload = () => {
      setIsLoaded(true);
    };

    script.onerror = () => {
      const errorMsg = 'Failed to load Turnstile script';
      setError(errorMsg);
      onError?.(errorMsg);
    };

    document.head.appendChild(script);

    return () => {
      // Cleanup script if component unmounts
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, [siteKey, onError]);

  useEffect(() => {
    if (!isLoaded || !containerRef.current || !window.turnstile || !siteKey) {
      return;
    }

    try {
      const id = window.turnstile.render(containerRef.current, {
        sitekey: siteKey,
        theme,
        size,
        callback: (token: string) => {
          setError(null);
          onVerify(token);
        },
        'error-callback': (error: string) => {
          const errorMsg = `Turnstile error: ${error}`;
          setError(errorMsg);
          onError?.(errorMsg);
        },
        'expired-callback': () => {
          setError('Verification expired');
          onExpire?.();
        },
      });

      setWidgetId(id);
    } catch (err) {
      const errorMsg = `Failed to render Turnstile: ${err}`;
      setError(errorMsg);
      onError?.(errorMsg);
    }

    return () => {
      if (widgetId && window.turnstile) {
        try {
          window.turnstile.remove(widgetId);
        } catch (err) {
          console.warn('Failed to remove Turnstile widget:', err);
        }
      }
    };
  }, [isLoaded, siteKey, theme, size, onVerify, onError, onExpire]);

  // Reset function that can be called from parent
  const reset = () => {
    if (widgetId && window.turnstile) {
      try {
        window.turnstile.reset(widgetId);
        setError(null);
      } catch (err) {
        console.warn('Failed to reset Turnstile widget:', err);
      }
    }
  };

  // Expose reset function to parent via ref
  useEffect(() => {
    if (containerRef.current) {
      (containerRef.current as any).reset = reset;
    }
  }, [widgetId]);

  if (!siteKey) {
    return (
      <div className={`text-sm text-gray-500 ${className}`}>
        Bot protection not configured
      </div>
    );
  }

  if (error) {
    return (
      <div className={`text-sm text-red-500 ${className}`}>
        {error}
      </div>
    );
  }

  return (
    <div className={className}>
      <div ref={containerRef} />
      {!isLoaded && (
        <div className="text-sm text-gray-500">
          Loading bot protection...
        </div>
      )}
    </div>
  );
}
