# AiLex Website - Prospect Integration Guide

## Overview

This document outlines the complete integration of prospect/waitlist signup functionality with the existing AiLex website forms. The integration connects to your PI Lawyer AI backend system for comprehensive lead management.

## What Was Implemented

### 1. Environment Configuration
- Updated `.env.example` with new environment variables
- Added support for both Vite and Next.js environment variable naming conventions
- Configured PI Lawyer AI backend URL and optional Turnstile bot protection

### 2. TypeScript Types
- Added comprehensive prospect data types in `client/src/types/index.ts`
- Includes `ProspectSignupRequest`, `ProspectSignupResponse`, `EmailVerificationRequest`, and `UTMParameters`

### 3. Utility Functions (`client/src/lib/utils.ts`)
- `getUTMParameters()`: Extracts UTM parameters from URL
- `storeUTMParameters()`: Persists UTM parameters in sessionStorage
- `getStoredUTMParameters()`: Retrieves stored UTM parameters
- `getCurrentPageUrl()`: Gets current page URL for tracking
- Enhanced email and phone validation functions

### 4. Prospect Service (`client/src/lib/prospectService.ts`)
- `ProspectService.signupProspect()`: Main prospect signup function
- `ProspectService.verifyEmail()`: Email verification handler
- `ProspectService.checkEmailVerificationStatus()`: Check verification status
- Convenience functions: `signupForNewsletter()` and `signupForContact()`
- Automatic UTM parameter injection and page tracking

### 5. GDPR Consent Component (`client/src/components/GDPRConsent.tsx`)
- Compliant consent collection with detailed privacy information
- Optional marketing consent checkbox
- Expandable privacy details section
- Proper GDPR compliance with required consent validation

### 6. API Endpoints
- `/api/prospects/signup`: Prospect signup endpoint that proxies to PI Lawyer AI backend
- `/api/prospects/verify-email`: Email verification endpoint (PUT and GET methods)
- Comprehensive error handling and validation
- Backward compatibility with existing APIs

### 7. Updated Contact Form (`client/src/components/ContactForm.tsx`)
- Integrated with prospect API while maintaining backward compatibility
- Added phone number field (optional)
- GDPR consent integration
- Enhanced validation and error handling
- Automatic UTM parameter capture

### 8. Updated Footer Newsletter (`client/src/components/Footer.tsx`)
- Integrated with prospect API
- GDPR consent flow for newsletter signups
- Enhanced UX with consent confirmation
- Backward compatibility maintained

### 9. Email Verification Page (`client/src/pages/VerifyEmail.tsx`)
- Dedicated page for email verification at `/verify-email`
- Handles verification tokens from email links
- Success/error states with appropriate messaging
- Responsive design matching site aesthetics

### 10. Routing Updates (`client/src/App.tsx`)
- Added routes for email verification page
- Supports both `/verify-email` and `/verify-email/:token` patterns

## Environment Variables Required

### For Development (.env.local)
```bash
# Existing variables
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
RESEND_API_KEY=your_resend_api_key

# New variables for prospect integration
VITE_PI_LAWYER_API_URL=https://your-pi-lawyer-ai.vercel.app
NEXT_PUBLIC_PI_LAWYER_API_URL=https://your-pi-lawyer-ai.vercel.app

# Optional: Turnstile bot protection
VITE_TURNSTILE_SITE_KEY=your_turnstile_site_key
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_turnstile_site_key
TURNSTILE_SECRET_KEY=your_turnstile_secret_key
```

### For Production (Vercel Dashboard)
Add the same variables in your Vercel project settings under Environment Variables for Production, Preview, and Development environments.

## API Integration Flow

### 1. Form Submission
1. User fills out contact form or newsletter signup
2. UTM parameters are automatically captured and stored
3. GDPR consent is validated
4. Data is sent to `/api/prospects/signup` endpoint
5. Endpoint forwards request to PI Lawyer AI backend
6. Response is returned to frontend
7. Legacy APIs are also called for backward compatibility

### 2. Email Verification
1. User receives verification email from PI Lawyer AI backend
2. User clicks verification link pointing to `/verify-email?token=...`
3. VerifyEmail page extracts token and calls verification API
4. Success/error message is displayed to user

### 3. UTM Tracking
1. UTM parameters are captured on page load
2. Parameters are stored in sessionStorage for persistence
3. All form submissions automatically include UTM data
4. Signup source is set to 'website' with specific page URL

## Data Flow

```
User Form Submission
        ↓
UTM Parameters Captured
        ↓
GDPR Consent Validated
        ↓
AiLex Website API (/api/prospects/signup)
        ↓
PI Lawyer AI Backend (/api/prospects/signup)
        ↓
Database Storage + Email Verification Sent
        ↓
Response to User
```

## Testing

### API Testing
Run the test script to verify all endpoints:
```bash
npm run test:api http://localhost:5000
```

### Manual Testing
1. Fill out contact form with GDPR consent
2. Check browser network tab for API calls
3. Verify UTM parameters are captured (add ?utm_source=test to URL)
4. Test email verification flow (requires backend setup)

## Backward Compatibility

- All existing form functionality is preserved
- Legacy API endpoints continue to work
- Existing database tables are still used
- No breaking changes to current user experience

## Next Steps

1. **Deploy Backend**: Ensure your PI Lawyer AI backend is deployed and accessible
2. **Configure Environment Variables**: Set up all required environment variables in Vercel
3. **Test Integration**: Verify the complete flow from form submission to email verification
4. **Monitor**: Check logs for any integration issues
5. **Optional**: Add Turnstile bot protection if desired

## Support

If you encounter any issues:
1. Check browser console for JavaScript errors
2. Verify environment variables are set correctly
3. Check Vercel function logs
4. Use the health endpoint: `/api/health`
5. Run the test script to identify specific failing endpoints

The integration maintains full backward compatibility while adding powerful prospect management capabilities to your AiLex website.
