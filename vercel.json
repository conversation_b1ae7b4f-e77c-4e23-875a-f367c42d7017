{"version": 2, "buildCommand": "pnpm run build", "outputDirectory": "dist", "installCommand": "pnpm install", "framework": "vite", "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}, {"source": "/((?!manifest\\.json|robots\\.txt|sitemap\\.xml|favicon\\.ico|.*\\.(png|jpg|jpeg|gif|svg|webp|ico)).*)", "destination": "/index.html"}], "headers": [{"source": "/manifest.json", "headers": [{"key": "Content-Type", "value": "application/manifest+json"}, {"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}